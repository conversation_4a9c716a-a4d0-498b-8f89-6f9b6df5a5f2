import React from 'react';
import { <PERSON><PERSON>, <PERSON>, Text, Page } from 'zmp-ui';
import { useNavigate } from 'zmp-ui';
import GameHeader from './GameHeader';
import heroImage from '@/static/welcome/hero.jpg';
import flowerImage from '@/static/flower.jpg';
import ensureLogoImage from '@/static/welcome/ensure-logo.jpg';
import decorImage from '@/static/welcome/decor.jpg';
import selectButtonImage from '@/static/select-button.jpg';
import logoBrandImage from '@/static/logo-brand.jpg';
import PrizesSection from './PrizesSection';
import { trackScreenView, trackGameStart, sendGA4Event } from '@/utils/ga4-tracking';

interface WelcomeScreenProps {
  onStart: () => void;
  sessionId: string;
}

const WelcomeScreen: React.FC<WelcomeScreenProps> = ({ onStart, sessionId }) => {
  const navigate = useNavigate();

  React.useEffect(() => {
    // Track screen view when component mounts
    trackScreenView('WelcomeScreen', 'Game');
  }, []);

  const handleStartGame = async () => {
    // Track game start clicked event using the provided sessionId
    await sendGA4Event({
      name: 'game_start_clicked',
      params: {
        screen_name: 'WelcomeScreen',
        button_location: 'footer',
        button_text: 'THAM GIA NGAY'
      }
    });
    await trackGameStart(sessionId);

    onStart();
    navigate('/upload');
  };

  return (
    <Page
      className="welcome-screen"
    >
      {/* Header */}
      <GameHeader />

      {/* Main Content Area */}
      <Box className="main-content" style={{ position: 'relative' }}>
        {/* Top positioned images */}
        <img
          src={ensureLogoImage}
          alt="Ensure Logo"
          style={{ position: 'absolute', top: 0, left: 0 }}
          className="ensure-logo"
        />
        <img
          src={decorImage}
          alt="Decor"
          style={{ position: 'absolute', top: 0, right: 0 }}
          className="decor-image"
        />

        {/* Hero Section */}
        <Box className="hero-section">
          <h1 className="primary-heading gradient-text">Bé Ngoan</h1>
          <p className="secondary-text gradient-text">lớn rồi có còn nhận được hoa hồng</p>

          <img src={heroImage} alt="Hero" className="hero-image !w-[80%]" />

          <p className="intro-text gradient-text">
            Cùng Ensure Gold tạo phiếu Bé Ngoan <br /> và thu thập hoa hồng về khoe Cha Mẹ
          </p>

          <img src={flowerImage} alt="Flower" className="flower-image" />

          <p className="cta-primary gradient-text">Tham gia và chia sẻ</p>
          <p className="cta-secondary gradient-text">để có cơ hội trúng nhiều quà tặng hấp dẫn</p>

          <PrizesSection className="pt-3" />
        </Box>
      </Box>

      {/* Selected Zone (Footer) */}
      <Box
        className="selected-zone"
      >
        {/* Action Buttons */}
        <Box className="action-buttons">
          <button className="image-button" onClick={handleStartGame}>
            <img src={selectButtonImage} alt="Start Game" />
            <span className="button-text gradient-text">THAM GIA NGAY</span>
          </button>
        </Box>

        {/* Brand Logo */}
        <img
          src={logoBrandImage}
          alt="Brand Logo"
          style={{
            position: 'absolute',
            bottom: '15px',
            right: '15px',
            width: 'auto',
            height: '20px',
            zIndex: 10
          }}
        />
      </Box>
    </Page>
  );
};

export default WelcomeScreen;
