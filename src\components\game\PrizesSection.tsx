import React from 'react';
import { Box, Text } from 'zmp-ui';
import valiImage from '@/static/prize/vali.jpg';
import milkImage from '@/static/prize/milk.jpg';

interface PrizesSectionProps {
  className?: string;
}

const PrizesSection: React.FC<PrizesSectionProps> = ({ className }) => {
  const labelStyle = {
    color: '#023C81',
    fontWeight: 590,
    fontStyle: 'normal',
    fontSize: '11px',
    lineHeight: '16px',
    letterSpacing: '0%',
    textAlign: 'center' as const,
    leadingTrim: 'cap',
    marginTop: 'auto'
  };

  const milkLabelStyle = {
    ...labelStyle,
    fontSize: '10px'
  };

  const prizeQuantityTextStyle = {
    fontWeight: 1000,
    fontStyle: 'normal',
    fontSize: '11px',
    lineHeight: '16px',
    letterSpacing: '0%',
    color: '#C81518'
  };

  const prizeQuantityContainerStyle = {
    padding: '8px 10px',
    borderTopLeftRadius: '10px',
    borderTopRightRadius: '10px',
    background: 'linear-gradient(180deg, #FFF7DB 0%, #FBEDBE 100%)'
  };

  return (
    <Box
      className={`prizes-wrapper ${className || ''}`}
      style={{
        width: '100%',
        maxWidth: '400px',
        margin: '0 auto'
      }}
    >
      <Box
        className="prize-quantities"
        style={{
          display: 'flex',
          justifyContent: 'space-around',
          gap: '10px',
          marginBottom: '0px'
        }}
      >
        <Box
          style={{
            ...prizeQuantityContainerStyle,
            display: 'inline-flex',
            justifyContent: 'center',
            alignItems: 'center'
          }}
        >
          <Text style={prizeQuantityTextStyle}>
            6 GIẢI NHẤT
          </Text>
        </Box>
        <Box
          style={{
            ...prizeQuantityContainerStyle,
            display: 'inline-flex',
            justifyContent: 'center',
            alignItems: 'center'
          }}
        >
          <Text style={prizeQuantityTextStyle}>
            15 GIẢI NHÌ
          </Text>
        </Box>
      </Box>
      <Box
        className="prizes-section-wrapper"
        style={{
          borderRadius: '12px',
          background: 'linear-gradient(87.19deg, #D7BD68 0%, #F4E09E 29.49%, #D7BD68 41.79%, #F4E09E 51.43%, #F4E09E 61.34%, #D7BD68 73.73%, #F4E09E 86.86%, #D7BD68 100%)',
          padding: '1.8px',
          width: '100%'
        }}
      >
        <Box
          className="prizes-section"
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            padding: '10px',
            borderRadius: '10.2px',
            background: 'linear-gradient(180deg, #FBEDBE 0%, #FFF7DB 50%, #FBEDBE 100%)',
            position: 'relative',
            width: '100%'
          }}
        >
          <Box
            className="prize-container"
            style={{
              flex: 1,
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'space-between',
              alignItems: 'center',
              padding: '0px',
              height: '100%'
            }}
          >
            <Box
              style={{
                width: '50%',
                display: 'flex',
                justifyContent: 'center'
              }}
            >
              <img
                src={valiImage}
                alt="Vali Prize"
                style={{
                  width: '100%',
                  height: 'auto',
                  objectFit: 'contain'
                }}
              />
            </Box>
            <Text style={labelStyle}>
              01 Vali kéo Azio Samsonite
            </Text>
          </Box>

          <Box
            className="divider"
            style={{
              width: '1px',
              height: '80%',
              position: 'absolute',
              left: '50%',
              transform: 'translateX(-50%)',
              border: '1px solid',
              borderImageSource: 'linear-gradient(90deg, #FBEDBE 4.97%, #F0E0A9 47.51%, #FBEDBE 90.06%)',
              borderImageSlice: 1
            }}
          />

          <Box
            className="prize-container"
            style={{
              flex: 1,
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'space-between',
              alignItems: 'center',
              padding: '0px',
              height: '100%'
            }}
          >
            <Box
              style={{
                width: '75%',
                display: 'flex',
                justifyContent: 'center'
              }}
            >
              <img
                src={milkImage}
                alt="Milk Prize"
                style={{
                  width: '100%',
                  height: 'auto',
                  objectFit: 'contain'
                }}
              />
            </Box>
            <Text style={milkLabelStyle}>
              02 lon sữa Ensure Gold 800g
            </Text>
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default PrizesSection;