// Layout and structural styles

// Game Header Styles
.game-header {
  height: 48px;
  background-color: #0c1bac;
  width: 100%;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1001;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

// Page Layout Structure
.zaui-page {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  margin: 0;
  padding: 0;
  padding-top: 48px; // Account for fixed header
}

// Main content area styles
.main-content {
  padding: 20px;
  flex: 1;
  overflow-y: auto;
}

// Selected Zone (Footer area)
.selected-zone {
  display: flex;
  flex-direction: column;
  justify-content: end;
  align-items: center;
  padding: var(--spacing-lg);
  padding-bottom: 40px;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
  min-height: 200px; // Ensure background image is visible
  z-index: 10;
  background-image: url('../static/select-zone.jpg');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;

  // No overlay - keep background image clear
  // Content stays on top of background
  >* {
    position: relative;
    z-index: 1;
  }

  // Action buttons at bottom of selected zone
  .action-buttons {
    margin: 0;
    padding: 0;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    // Image-based button styling
    .image-button {
      position: relative;
      display: inline-block;
      border: none;
      background: transparent;
      padding: 0;
      cursor: pointer;
      margin: 0;

      img {
        display: block;
        width: 100%;
        height: auto;
        max-width: 300px;
      }

      .button-text {
        width: 100%;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        color: var(--white);
        text-align: center;
        leading-trim: both;
        text-edge: cap;
        font-size: 20px;
        font-style: normal;
        font-weight: 900;
        line-height: 24px;
        /* 80% */
        pointer-events: none;
        white-space: nowrap;
        padding-bottom: 10px;
        padding-left: 40px;
      }


      &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
      }
    }

    // Fallback for regular buttons if needed
    button:not(.image-button) {
      min-height: 48px;
      font-weight: 600;
      border-radius: var(--border-radius-lg);
      background: var(--primary-color);
      border: none;
      color: var(--white);
      padding: 0 var(--spacing-lg);
    }

    .navigation-buttons {
      display: flex;
      gap: 20px;
      justify-content: center;
      width: 100%;

      // Single button - full width with max constraint
      &:has(.image-button:only-child) {
        .image-button {
          flex: 0 0 auto;
          max-width: 300px;
        }
      }

      // Two buttons - equal width distribution
      &:has(.image-button:nth-child(2)) {
        .image-button {
          flex: 1 1 0;
          max-width: 180px;
          min-width: 140px;

          img {
            max-width: 100%;
          }

          .button-text {
            font-size: 18px;
            padding-left: 30px;
          }
        }
      }

      // Three or more buttons - compact layout
      &:has(.image-button:nth-child(3)) {
        flex-wrap: wrap;

        .image-button {
          flex: 0 1 calc(33.333% - 14px);
          max-width: 150px;
          min-width: 120px;

          .button-text {
            font-size: 16px;
            padding-left: 20px;
          }
        }
      }
    }
  }
}

// Common Screen Styles
.progress-indicator {
  display: flex;
  justify-content: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-xl);

  .progress-step {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    color: var(--white);
    background: var(--gray);
    transition: all var(--transition-normal);

    &.active {
      background: var(--primary-color);
      transform: scale(1.1);
    }

    &.completed {
      background: var(--success-color);
    }
  }
}

// Game Screen Base Styles
.welcome-screen,
.image-upload-screen,
.survey-screen,
.results-screen {
  position: relative;
  min-height: 100vh;
  padding-bottom: 140px; // Space for selected-zone footer
  background-image: url('../static/bg.jpg');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  background-attachment: fixed;

  .selected-zone {
    // Keep full width for background image
    // Content can be centered with inner containers
  }
}

// Loading Screen Styles
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  text-align: center;
  color: var(--white);

  .loading-spinner {
    width: 60px;
    height: 60px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid var(--white);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: var(--spacing-lg);
  }

  .loading-steps {
    margin-top: var(--spacing-lg);
    background: rgba(255, 255, 255, 0.9);
    padding: var(--spacing-md);
    border-radius: var(--border-radius-md);
    backdrop-filter: blur(5px);
    box-shadow: var(--shadow-md);
    color: var(--dark-gray);

    .loading-step {
      padding: var(--spacing-xs) 0;

      &.active {
        color: var(--primary-color);
        font-weight: bold;
      }
    }
  }
}

// Animations
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0% {
    opacity: 1;
  }

  50% {
    opacity: 0.7;
  }

  100% {
    opacity: 1;
  }
}

// Responsive Design
@media (max-width: 768px) {
  .game-app {
    .game-timer {
      top: var(--spacing-sm);
      right: var(--spacing-sm);
      font-size: var(--font-size-xs);
    }
  }

  .selected-zone {
    .action-buttons {
      .navigation-buttons {

        // Keep horizontal layout for 2 buttons on mobile
        &:has(.image-button:nth-child(2)):not(:has(.image-button:nth-child(3))) {
          flex-direction: row;
          gap: 15px;

          .image-button {
            flex: 1 1 0;
            max-width: 160px;
            min-width: 120px;

            .button-text {
              font-size: 16px;
              padding-left: 20px;
            }
          }
        }

        // Stack buttons vertically if 3 or more
        &:has(.image-button:nth-child(3)) {
          flex-direction: column;
          gap: 10px;

          .image-button {
            flex: 0 0 auto;
            width: 100%;
            max-width: 250px;
          }
        }
      }
    }
  }
}

// Debug styles (development only)
.debug-info {
  position: fixed;
  bottom: var(--spacing-md);
  left: var(--spacing-md);
  background: rgba(0, 0, 0, 0.8);
  color: var(--white);
  padding: var(--spacing-sm);
  border-radius: var(--border-radius-sm);
  font-size: var(--font-size-xs);
  max-width: 300px;
  z-index: 999;

  details {
    summary {
      cursor: pointer;
      margin-bottom: var(--spacing-xs);
    }

    pre {
      max-height: 200px;
      overflow: auto;
      font-size: 10px;
    }
  }
}